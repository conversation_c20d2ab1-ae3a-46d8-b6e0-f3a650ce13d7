package com.kanzhun.foundation.bean;

import com.kanzhun.foundation.api.model.ChildInfo;
import com.kanzhun.foundation.api.model.UserMood;

import java.io.Serializable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/11
 */
public class SMSLoginModel implements Serializable {
    /**
     * "userId": "xxxxxxxxx", // 用户ID，加密字符串
     * "nickName": "xxx", // 昵称
     * "gender":1, // 性别，1-男，2-女
     * "birthday": "1994-01-01", // 出生年月日 yyyy-MM-dd
     * "location": "北京市朝阳区", // 生活所在地  TODO 待确定code或拆分参数
     * "school": "北京大学", // 毕业学校
     * "phase":1, // 账户阶段状态 1-注册未邀请 2-注册已邀请 3-已激活
     * "banTime": 1551665159037, // 封禁截止日期，空表示正常
     * "sk": "xxxxxxxxxxxxxxx", // 签名秘钥
     * "apmUid": "zzzzzzzz", // apm交互ID，加密字符串
     */


    public String userId;  //用户ID，加密字符串
    public String nickName; //昵称
    public String nickNamePy;//昵称拼音
    public String avatar;//头像
    public String tinyAvatar;//头像缩略图
    public String liveVideo;//头像 livePhoto的视频 地址 可空
    public int gender;  //性别，1-男，2-女
    public String birthday; //出生年月日 yyyy-MM-dd
    public String school; //毕业学校
    public int schoolLevel;//学校等级 0未定级, 1一本,2 海外等同一本, 3一本也招二本,4一本也招专科成教
    public int schoolArea;//学校区域，0-不定，1-国内，2-海外
    public int phase;  // 账户阶段状态 1-注册未邀请 2-注册已邀请 3-已激活

    public String sk;//签名秘钥
    public String apmUid; //apm交互ID long 类型
    public int matchStatus = 1;//关系：1-无匹配，2-相识中，3-情侣
    public int communityLocked;//  社区封禁状态 0-不封禁 1-封禁
    public int profileLocked; //0 未锁定 1 锁定
    public UserMood mood;
    public int shakeLocked;//0 未锁定 1:锁定

    public ChildInfo childInfo;//孩子基本信息，里边字段均可能为空
    public int accountType;// 0 未知需要选择类型 1 自己找 2 帮人找
    public String wechatCode;//微信的code 如果此值不为空，需要走注册流程

    public int marryIntent;//结婚意向，1 有计划接受 2 无计划不接受  若跳过，由服务端处理均返回 1
    public int marryIntentCertStatus;// 结婚意向审核状态  1 待审核 2 已驳回

    public int inviteCodeType;// 1 无邀请码 2 有邀请码无申请入口  3 有邀请码有申请入口
    public String customerInviteCodeH5Url;
    public String watermark;

    public int meetPlanUserStatus;//0:未开启 10-等待中；20-排序中；30-匹配中；40-聊天中；50-已结束；
    public String meetPlanId;//见面计划id


}
