package com.kanzhun.foundation.api.base;

import android.text.TextUtils;

import com.kanzhun.utils.URLUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/23
 */
public class URLConfig {
    public static List<String> FILE_UPLOAD_LIST = Arrays.asList();

    public static List<String> LOG_UPLOAD_LIST = Arrays.asList();

    public static final String URL_UPLOAD_FILE = "media/upload/file";//上传文件

    public static final String URL_UPLOAD_AVATAR = "media/upload/avatar";//上传头像

    public static final String URL_UPLOAD_IMAGE = "media/upload/image";//上传图片

    public static final String URL_UPLOAD_VIDEO = "media/upload/video";//上传视频

    public static final String URL_UPLOAD_VOICE = "media/upload/audio";//上传音频

    public static final String URL_REGISTER_ACCOUNT_STATUS = "orange/register/accountStatus";//查询账号异常状态
    public static final String URL_ACTIVITY_COUPON_GET_PROCESS_BAR = "orange/activity/coupon/getProcessBar";//用户完善流程数据查询
    public static final String URL_PROFILE_WORK_OTHERUSERTIPS = "orange/user/profile/work/otherUserTips";//其他用户工作信息提示

    public static final String URL_ORANGE_DIC_PROFILE_TAG = "orange/dic/profile/tag";//形象标签列表
    public static final String URL_ORANGE_LIKE_EACH_OTHER_LIST = "orange/likeEachOther/list";//查询互相喜欢列表
    public static final String URL_ORANGE_LIKE_EACH_OTHER_READ = "orange/likeEachOther/read";//已读双方互相喜欢

    public static final String URL_GEE_CAPTCHA_REGISTER = "orange/auth/geeInit";//极验验证初始化接口

    public static final String URL_GEE_CAPTCHA_VALIDATE = "orange/auth/geeCheck";//极验证校验并发送验证码

    public static final String URL_SMS_LOGIN = "orange/auth/smsLogin";//通过手机验证码登录
    public static final String URL_RECOMMEND_TAG_IDEAL_PARTNER = "orange/recommend/tag/idealPartner";//查询理想型匹配的标签
    public static final String URL_RECOMMEND_TAG_IDEAL_PARTNER_MATCH = "orange/recommend/tag/idealPartner/match";//查询理想型匹配的标签

    public static final String URL_SETTING_INFO = "orange/mine/settingInfo"; //获取设置的信息

    public static final String URL_SMS_LOGOUT = "orange/auth/logout"; //退出登录
    public static final String URL_GET_MARRY_INTENT = "orange/user/getMarryIntent"; //【一年内结婚】获取用户结婚意向
    public static final String URL_SUBMIT_MARRY_INTENT = "orange/user/submitMarryIntent"; //【一年内结婚】提交申请有计划结婚
    public static final String URL_ADD_MARRY_INTENT = "orange/user/addMarryIntent"; //【一年内结婚】添加用户结婚意向

    public static final String URL_CHECK_INVITE = "orange/register/checkInvite";//校验邀请码
    public static final String URL_AUTH_CHANGE_ACCOUNT_TYPE = "orange/auth/changeAccountType";//切换账号类型

    public static final String URL_REGISTER_ADD_INFO = "orange/register/addInfo";//填写激活信息
    public static final String URL_MINE_RED_POINT_CLEAR = "orange/mine/moment/redPoint/clear";//清除动态红点
    public static final String URL_ORANGE_USER_AB_FACE_GET_BY_ID = "orange/user/abFace/getById";//根据id获取ab面信息
    public static final String URL_ORANGE_USER_UPDATE_USER_TAG = "orange/user/updateUserTag";//更新tag信息
    public static final String URL_ORANGE_USER_UPDATE_INTEREST_TAG = "orange/user/updateInterestTag";//更新兴趣爱好标签
    public static final String URL_ORANGE_REGISTER_FINISH_COMPLETE = "orange/register/finishComplete";//完成首善接口

    public static final String URL_COMMON_ADDRESS = "orange/common/address";//查询所有省市地区

    public static final String URL_GET_PROFILE_SHARE = "orange/share/getProfileShare";//获取个人页分享信息

    public static final String URL_COMMON_SCHOOL_SUGGEST = "orange/common/schoolSuggest";//学校suggest信息

    public static final String URL_SURVEY_QUERY = "orange/survey/query";//获取测试题

    public static final String URL_SYSTEM_CONFIG = "orange/system/config";//查询功能配置信息

    public static final String URL_RECOMMEND_TAG_SSE = "orange/recommend/tag/sse";

    public static final String URL_RECOMMEND_TAG_QUESTIONNAIRE = "orange/recommend/tag/questionnaire";

    public static final String URL_ORANGE_USER_GET_UPGRADE_CERT = "orange/user/getUpgradeCert";//获取升级认证信息

    public static final String URL_USER_TAB_INFO = "orange/mine/tabInfo";//获取用户tab页面信息接口
    public static final String URL_USER_MINE_GET_MY_ACTIVITY = "orange/mine/getMyActivity";//获取我的tab页用户活动卡片

    public static final String URL_VERIFY_QUERY_SCHOOL = "orange/cert/edu/querySchool";//根据学校名称查询和校验

    public static final String URL_USER_ME_INFO = "orange/user/me";//个人信息自查接口
    public static final String URL_ORANGE_USER_SCHOOL_GET = "orange/user/profile/school/get";//查询学历和学校审核结果
    public static final String URL_ORANGE_CERT_EDU_CERT_TYPE_LIST = "orange/cert/edu/certTypeList";//学历认证-认证项列表

    public static final String URL_USER_GET_INDUSTRY_CODE = "orange/user/getIndustryCode";// 获取行业列表
    public static final String URL_USER_GET_QR_BY_USER_ID = "orange/qr/getByUserId/v1";// 根据用户id获取用户二维码

    public static final String URL_VERIFY_EDU_OVERSEAS = "orange/cert/edu/overseas";//学历认证_教留服证书编号
    public static final String URL_USER_UPDATE_LOVE_GOAL = "orange/user/updateLoveGoal";//更新恋爱目标
    public static final String URL_USER_UPDATE_MARITAL_STATUS = "orange/user/updateMaritalStatus";//更新婚姻状态
    public static final String URL_USER_UPDATE_CHILD_PREFERENCE = "orange/user/updateChildbearingPreference";//更新生育偏好
    public static final String URL_USER_UPDATE_SMOKING_HABIT = "orange/user/updateSmokingHabit";//抽烟
    public static final String URL_USER_UPDATE_DRINKING_HABIT = "orange/user/updateDrinkingHabit";//喝酒
    public static final String URL_USER_UPDATE_ETHNICITY = "orange/user/updateEthnicity";//更新民族
    public static final String URL_ORANGE_COMMON_ETHNICITY = "orange/common/ethnicity";//查询民族列表
    public static final String URL_ORANGE_USER_GET_ABOUT_ME_SUGGEST = "orange/user/getAboutMeSuggest";//查询关于我推荐更新文案

    public static final String URL_VERIFY_ID_NUM = "orange/cert/face/idNum";//身份证校验

    public static final String URL_VERIFY_FACE = "orange/cert/face/submit";//人脸实名认证

    public static final String URL_VERIFY_FACE_QUERY = "orange/cert/face/query";//查询实名认证结果
    public static final String URL_FACE_GET_ORDER_ID = "orange/cert/face/security/getOrderId";//获取安全sdk订单号
    public static final String URL_FACE_VERIFY_RESULT = "orange/cert/face/security/verifyResult";//安全sdk提交订单号，校验人脸结果

    public static final String URL_VERIFY_EDU_CHSI = "orange/cert/edu/chsi";//学历认证-学信网

    public static final String URL_VERIFY_EDU_DIPLOMA = "orange/cert/edu/diploma";//学历认证_毕业证学位证编号

    public static final String URL_VERIFY_EDU_DIPLOMA_PIC = "orange/cert/edu/diplomaPic";//学历认证_毕业证学位证照片

    public static final String URL_A_B_IMPRESSION_GET = "orange/user/getABFaceSubject";//获取AB主题面数据

    public static final String URL_A_B_IMPRESSION_UPDATE = "orange/user/updateABFace";//更新AB面

    public static final String URL_A_B_IMPRESSION_ADD = "orange/user/addABFace";//新增AB面
    public static final String URL_ORANGE_CERT_CHSI_ONLINE = "orange/cert/edu/chsi/online";//学历认证-学信网在线验证
    public static final String URL_CERT_COMPANY_GETEMAILSUFFIX = "orange/cert/company/getEmailSuffix";//公司认证-获取邮箱后缀
    public static final String URL_CERT_COMPANY_CERT_TYPE_LIST = "orange/cert/company/certTypeList";//公司认证-认证项列表

    public static final String URL_A_B_IMPRESSION_DELETE = "orange/user/delABFace";//删除AB面

    public static final String URL_A_B_IMPRESSION_SORT = "orange/user/sortABFace";//排序AB面

    public static final String URL_PROFILE_INFO = "orange/user/getProfileInfo";//查询用户个人页信息
    public static final String URL_ACTIVITY_ALBUM_GETPERSONALALBUM = "orange/activity/album/getPersonalAlbum";//查询用户个人页看准相册
    public static final String URL_PARENT_BIND_INFO = "orange/parent/bindInfo";//父母查询账号绑定信息

    public static final String URL_USER_QUESTION_TEMPLATE = "orange/user/questionAnswerTemplate";//获取问答模板
//    public static final String URL_USER_QUESTION_TEMPLATE = "https://api.weizhipin.com/mock/1975/api/orange/user/questionAnswerTemplate";//获取问答模板

    public static final String URL_PROFILE_META = "orange/user/getProfileMeta";//查询用户个人页编辑态信息
    public static final String URL_SYSTEM_LOVE_GOAL_LIST = "orange/system/loveGoalList";//查询恋爱目标列表
    public static final String URL_COMMON_BIRTH_YEAR_RANGE = "orange/common/birthYearRange";//查询生日可选年份范围
    public static final String URL_USER_TIPS = "orange/user/tips";//个人信息页提示条
    public static final String URL_USER_TIPS_v2 = "orange/user/tipsV2";//个人信息页提示条
    public static final String URL_USER_DEVICE_LIST = "orange/user/device/list";//获取用户设备列表
    public static final String URL_USER_DEVICE_DELETE = "orange/user/device/delete";//删除用户设备
    public static final String URL_USER_ACTIVITY_ALBUM_READ = "orange/activity/album/read";//个人页相册照片已读
    public static final String URL_QR_GET = "orange/qr/get";//获取用户信息二维码
    public static final String URL_MOOD_ICON_LIST = "orange/mood/iconList";//获取心情icon列表
    public static final String URL_MOOD_EACH_LIKE_LIST = "orange/mood/eachLike/list";//互相喜欢列表

    public static final String URL_USER_ADD_QUESTION_ANSWER = "orange/user/addQuestionAnswer";//新增问答
//    public static final String URL_USER_ADD_QUESTION_ANSWER = "https://api.weizhipin.com/mock/1975/api/orange/user/addQuestionAnswer";//新增问答

    public static final String URL_USER_UPDATE_QUESTION_ANSWER = "orange/user/updateQuestionAnswer";//更新问答

    public static final String URL_USER_QUESTION_ANSWER_SHARE = "orange/user/questionAnswerShare";//获取问答分享详情信息
//    public static final String URL_USER_UPDATE_QUESTION_ANSWER = "https://api.weizhipin.com/mock/1975/api/orange/user/updateQuestionAnswer";//更新问答

    public static final String URL_USER_DEL_QUESTION_ANSWER = "orange/user/delQuestionAnswer";//删除问答
//    public static final String URL_USER_DEL_QUESTION_ANSWER = "https://api.weizhipin.com/mock/1975/api/orange/user/delQuestionAnswer";//删除问答

    public static final String URL_GET_GUIDE_ITEMS = "orange/user/getGuideItems";// 获取基本信息引导项

    // https://api.weizhipin.com/project/1975/interface/api/744382
    public static final String URL_RECOMMEND_TAG_CONTENT_GENERATE = "orange/recommend/tag/content/generate"; // 标签生成文案

    public static final String URL_USER_GET_IDEAL_PARTNER = "orange/user/getIdealPartner"; // 查询我的标签
    public static final String URL_MEETUP_PLAN_FINISH_DIALOG = "orange/meetup/plan/finishDialog"; // 见面计划结束弹窗
    public static final String URL_COMPLETE_GUIDE_RECOMMEND_INFO = "orange/user/complete/guide/recommendInfo"; // 获取引导弹窗推荐数据

    public static final String URL_ME_STATURE_UPDATE = "orange/user/updateHeight";// 更新身高
    public static final String URL_ME_WEIGHT_UPDATE = "orange/user/updateWeight";// 更新体重

    public static final String URL_ME_INCOME_UPDATE = "orange/user/updateIncome";// 更新年收入

    public static final String URL_ME_RESIDENCE_UPDATE = "orange/user/updateHukou";// 更新户口所在地

    public static final String URL_ME_HOUSE_CAR_UPDATE = "orange/user/updateAssets";// 更新房车

    public static final String URL_ME_HOMETOWN_UPDATE = "orange/user/updateHometown";// 更新家乡

    public static final String URL_ME_LIVING_PLACE_UPDATE = "orange/user/address";// 更新现居地

    public static final String URL_ME_BIRTHDAY_UPDATE = "orange/user/updateBirthday";// 更新出生日期

    public static final String URL_ME_OCCUPATION_UPDATE = "orange/user/updateCareer";// 更新职业
    public static final String URL_ME_UPDATE_COM_INFO = "orange/user/updateComInfo";// 更新工作相关信息
    public static final String URL_COMMON_CHECK_COMPANY = "orange/common/checkCompany";// 公司名检测

    public static final String URL_USER_UPDATE_AVATAR = "orange/user/updateAvatar";//更新形象照

    public static final String URL_USER_UPDATE_NICK_NAME = "orange/user/updateNickName";//更新昵称

    public static final String URL_USER_ADD_STORY = "orange/user/addStory";//新增故事

    public static final String URL_USER_UPDATE_STORY = "orange/user/updateStory";//更新故事

    public static final String URL_USER_DEL_STORY = "orange/user/delStory";//删除故事
    public static final String URL_DATA_PUSH_BEHAVIOR = "orange/data/pushBehavior";//用户行为上报（站内push）

    public static final String URL_USER_SORT_STORY = "orange/user/sortStory";//排序故事

    public static final String URL_PROFILE_CERT = "orange/user/getProfileCert";//查询审核结果

    public static final String URL_ADD_PROFILE_IMG = "orange/user/addProfileImg";//新增个人页图片
    public static final String URL_DEL_PROFILE_IMG = "orange/user/delProfileImg";//删除个人页图片
    public static final String URL_UPDATE_PROFILE_IMG = "orange/user/updateProfileImg";//更新个人页图片
    public static final String URL_SORT_PROFILE_IMG = "orange/user/sortProfileImg";//排序个人页图片


    public static final String URL_USER_UPDATE_INTRO = "orange/user/updateIntro";//更新个性简介
    public static final String URL_CHAT_READ_MESSAGE = "orange/chat/readMessage";//读消息
    public static final String URL_USER_UPDATE_FAMILY_DESC = "orange/user/updateFamilyDesc";//更新家庭情况描述
    public static final String URL_USER_UPDATE_INTEREST = "orange/user/updateInterest";//更新兴趣爱好
    public static final String URL_USER_UPDATE_SINGLE_REASON = "orange/user/updateSingleReason";//更新单身原因
    public static final String URL_USER_UPDATE_IDEAL_PARTNERDESC = "orange/user/updateIdealPartnerDesc";//更新理想的另一半描述

    public static final String URL_QUESTION_ANSWER_SORT = "orange/user/sortQuestionAnswer";//排序问答

    public static final String URL_MATCHING_USER_INFO = "orange/match/user";//获取推荐用户

    public static final String URL_PROFILE_RECOMMEND = "orange/user/getProfileRecommend";//查询用户个人页推荐信息

    public static final String URL_BLOCK_INFO = "orange/user/getBlockInfo";//获取阻断信息状态

    public static final String URL_BLOCK_MATCH_GUIDE = "orange/match/guide";//查询匹配页跳转引导

    public static final String URL_MATCH_LIKE = "orange/match/like";//发送喜欢
    public static final String URL_MATCH_CHAT = "orange/profile/reply";//发送回复聊天

    public static final String URL_MATCH_FAVOR_FILTER = "orange/match/getFavorFilter";//查询匹配偏好

    public static final String URL_MATCH_UPDATE_FAVOR_FILTER = "orange/match/updateFavorFilter";//更新匹配偏好

    public static final String URL_MATCH_SKIP = "orange/match/skip";//无感跳过该用户

    public static final String URL_GET_FRIENDS = "orange/sync/friends";//同步好友信息

    public static final String URL_GET_NOTICE_LIST = "orange/chat/notice/list";//同步小秘书通知列表
    public static final String URL_GET_THUMB_LIST = "orange/thumb/list";//点赞列表

    public static final String URL_SYNC_CONVERSATION = "orange/sync/chat";//同步会话

    public static final String URL_SYNC_CONVERSATION_TAB_SUMMARY = "orange/chat/getTabSummary";//查询喜欢我/我喜欢/历史综合信息

    public static final String URL_SYNC_MATCH_PAGE = "orange/user/matchPage";//查询匹配模式页面信息

    //chat start
    public static final String URL_CHAT_GET_HISTORY = "orange/chat/getHistory";//查询历史会话

    public static final String URL_CHAT_LIKE_ME_LIST = "orange/chat/likeMeList";//喜欢我的列表

    //chat end

    public static final String URL_CHAT_GET_DATE_CARD = "orange/chat/getDateCard";// 查询相识卡内容

    public static final String URL_CHAT_REPLY_DATE_CARD = "orange/chat/replyDateCard";// 相识回复接口

    public static final String URL_CHAT_GET_LOVE_CARD = "orange/chat/getLoveCard";// 查询表白信内容

    public static final String URL_CHAT_REPLY_LOVE_CARD = "orange/chat/replyLoveCard";// 表白信回复接口

    public static final String URL_CHAT_CHECK_CARD_STATUS = "orange/chat/checkCardStatus";// 校验相识/表白信发送状态

    public static final String URL_CHAT_GET_CARD_PROCESS = "orange/chat/getCardProcess";// 获取会话中表白信/相识卡进度

    public static final String URL_CHAT_SEND_DATE_CARD = "orange/chat/sendDateCard";// 发送相识卡

    public static final String URL_CHAT_SEND_LOVE_CARD = "orange/chat/sendLoveCard";// 发送表白信

    //video_meeting

    public static final String URL_VIDEO_MEETING_CREATE = "orange/link/create";//创建会议房间

    public static final String URL_VIDEO_MEETING_INFO = "orange/link/info";//查询房间信息

    public static final String URL_VIDEO_MEETING_HEARTBEAT_REPORT = "orange/link/heartbeat/report";//心跳上报

    public static final String URL_VIDEO_MEETING_INVITE = "orange/link/invite";//发起呼叫

    public static final String URL_VIDEO_MEETING_INVITE_CANCEL = "orange/link/inviteCancel";//取消呼叫

    public static final String URL_VIDEO_MEETING_INVITE_ACCEPT = "orange/link/inviteAccept";//接受聊天

    public static final String URL_VIDEO_MEETING_INVITE_REFUSE = "orange/link/inviteRefuse";//拒绝呼叫

    public static final String URL_VIDEO_MEETING_LEAVE = "orange/link/leave";//离开聊天

    public static final String URL_GET_MSG_HISTORY = "orange/chat/getMsgHistory";//获取历史消息

    public static final String URL_GET_MSG_BY_ID = "orange/chat/messages";//查询消息详情

    public static final String URL_MOOD_LIST = "orange/system/moodList";//查询心情列表

    public static final String URL_MOOD_UPDATE = "orange/user/updateMood";//更新个人心情

    public static final String URL_CHAT_DEL_FRIEND = "orange/chat/delFriend";//取消好友匹配

    public static final String URL_CHAT_END_DATE = "orange/chat/endDate";//结束相识模式

    public static final String URL_CHAT_END_LOVE = "orange/chat/endLove";//结束情侣模式

    public static final String URL_CHAT_WITHDRAW = "orange/chat/withdraw";//撤回消息

    public static final String URL_TOKEN_UPDATE = "orange/token/update";//更新推送令牌
    public static final String URL_ORANGE_LIMITED_ACTIVITY_TASK_HIDE_TASK_FIND = "orange/limited/activity/task/hideTask/find";//隐藏任务获得积分
    public static final String URL_ORANGE_LIMITED_ACTIVITY_TASK_FINDCOMPLETE = "orange/limited/activity/task/findComplete";//找cp完成任务接口

    public static final String URL_TOKEN_DELETE = "orange/token/delete";//删除推送令牌

    public static final String URL_REPORT_REASON = "orange/advice/getReportReason";//查询举报类型

    public static final String URL_FEEDBACK_REASON = "orange/advice/getMatchFeedbackReason";//查询匹配反馈原因

    public static final String URL_REPORT_ADD = "orange/advice/addReport";//新增举报

    public static final String URL_VERSION_QUERY = "orange/version/query";//查询版本信息
    public static final String URL_ORANGE_COMMON_DIALOG_F1 = "orange/common/dialog/f1";//F1弹窗接口
    public static final String URL_ORANGE_HOME_POP = "orange/home/<USER>";//首页弹窗接口

    public static final String URL_ADD_FEEDBACK = "orange/advice/addFeedback";//新增反馈

    public static final String URL_DELETE_HISTORY_CONVERSATION = "orange/chat/delHistory";//删除历史会话

    public static final String URL_SETTING_UPDATE = "orange/user/updateSetting";//key: 101 不让任何人看到我  102 个性化推荐// value 1 开启 0 关闭
    public static final String URL_FILM_MY_WASH = "orange/film/myWash";//胶片我冲洗的列表
    public static final String URL_FILM_WASH_ME = "orange/film/washMe";//胶片冲洗我的

    public static final String URL_GET_CANCELLATION_REASON = "orange/destroy/reasonList";//获取注销原因
    public static final String URL_POST_USER_UPDATE_SCHOOL = "orange/user/updateSchool";//更新学校和学历
    public static final String URL_ORANGE_THUMB_ADD = "orange/thumb/add";//点赞
    public static final String URL_ORANGE_THUMB_CANCEL = "orange/thumb/cancel";//取消点赞

    public static final String URL_DESTROY_CHECK = "orange/destroy/check";//注销检查
    public static final String URL_CANCELLATION = "orange/destroy/submit";//注销

    public static final String URL_LOG_UPLOAD = "orange/app/saveLog"; //上传本地tlog
    public static final String URL_DEVICE_BAN_CHECK = "orange/device/ban/check"; //查询用户二次人脸处置详情

    public static final String URL_USER_CERT_LIST = "orange/user/getCertList";//查询我的认证列表
    public static final String URL_PUBLIC_CONFIG = "orange/system/public/config";//查询公共配置
    public static final String URL_USER_MINE_NOVICETASK = "orange/mine/noviceTask";//新手任务

    public static final String URL_COMPANY_SUGGEST = "orange/cert/company/suggest";//公司认证-公司联想推荐

    public static final String URL_USER_COMPANY_INFO = "orange/user/profile/work/get";//公司认证-查询工作信息审核结果
    public static final String URL_USER_MARRY_INFO = "orange/cert/marriage/getFaceInfo";//婚姻认证实名数据获取
    public static final String URL_USER_MARRY_CERT_RESULT = "orange/cert/marriage/get";//获取婚姻认证结果
    public static final String URL_USER_MARRY_CERT_SUBMIT = "orange/cert/marriage/submit";//获取婚姻认证结果
    public static final String URL_MARRIAGE_MATERIAL_SUBMIT = "orange/cert/marriage/submit";//婚姻材料上传提交

    public static final String URL_CERT_COMPANY_EMAIL_EXPIRE ="orange/cert/company/email/expire";//公司认证-用户主动失效邮箱认证

    public static final String URL_USER_COMPANY_SHORT = "orange/cert/company/short";//公司认证-公司获取简称

    public static final String URL_COMPANY_CERT_SUBMIT = "orange/cert/company/submit";//公司认证-提交认证
    public static final String URL_CERT_COMPANY_FEEDBACK_EMAILSUFFIX = "orange/cert/company/feedback/emailSuffix";//公司认证-反馈邮箱后缀

    public static final String URL_USER_CERT_STATUS = "orange/user/getUserCertStatus";//查询其他用户认证状态
    public static final String URL_USER_CERT_STATUS_V2 = "orange/user/getUserCertStatusV2";//查询其他用户认证状态

    public static final String URL_MOMENT_PROFILE_ME = "orange/moment/profile/me";//查询自己动态聚合页

    public static final String URL_MOMENT_PROFILE_USER = "orange/moment/profile/user";//查询他人动态聚合页

    public static final String URL_MOMENT_HISTORY_ME = "orange/moment/history/me";//查询自己聚合页动态列表

    public static final String URL_MOMENT_HISTORY_USER = "orange/moment/history/user";//查询他人聚合页动态列表

    public static final String URL_COMMUNITY_INTEREST_SEND = "orange/community/interest/send";//发起想认识

    public static final String URL_MOMENT_INFO = "orange/moment/info";//查询动态详情
    public static final String URL_FILM_MOMENT_QUOTA = "orange/film/momentQuota";//查询胶片动态权益

    public static final String URL_MOMENT_REPLAY_LIST = "orange/moment/replyList";//查询评论列表

    public static final String URL_MOMENT_THUMB_LIST = "orange/moment/thumbList";//动态的点赞列表

    public static final String URL_MOMENT_VISIBLE_UPDATE = "orange/moment/visible/update";//修改动态可见范围

    public static final String URL_MOMENT_DELETE = "orange/moment/delete";//删除动态

    public static final String URL_MOMENT_REPLY = "orange/moment/reply";//评论动态

    public static final String URL_MOMENT_COMMENT_REPLAY_LIST = "orange/moment/comment/replyList";//查询回复评论列表

    public static final String URL_MOMENT_COMMENT_REPLY = "orange/moment/comment/reply";//回复评论


    public static final String URL_H5_PROTOCOL_FAQ_FACE = "faq.html#/?type=face";//常见问题指导和400客服

    public static final String URL_H5_PROTOCOL_CERT = "cert.html?view=eduQRCodeGuide";//法务协议相关,学信网在线验证吗

    public static final String URL_H5_SOCAIL_SECURITY_GUIDE = "help.html#/socail-security-guide";//个人社保参保证明
    public static final String URL_H5_HOW_RETENTION_SERVICE_NUM_IDENTIFY = "cert.html#/edu-cscse-guide";//如何获取教留服编号
    public static final String URL_H5_HOW_GET_WX_WORK_CARD = "help.html#/how-get-weweork";//如何获取企业微信名片
    public static final String URL_H5_HOW_GET_DD_WORK_CARD = "help.html#/how-get-dingding";//如何获取钉钉名片

    public static final String URL_GET_SOCIAL_NOTIFY_COMMENT = "orange/community/interact/list";//查询评论和点赞列表

    public static final String URL_SQUARE_HEAD = "orange/square/head";//查询广场头部

    public static final String URL_SQUARE_FEED = "orange/moment/feed";//查询动态feed流

    public static final String URL_MOMENT_PUBLISH = "orange/moment/publish";//发布动态

    public static final String URL_GET_SOCIAL_NOTIFY_INTEREST = "orange/community/interest/list";//查询想认识列表

    public static final String URL_DELETE_SOCIAL_NOTIFY = "orange/community/notice/delete";//删除通知

    public static final String URL_MOMENT_THUMB_ADD = "orange/moment/thumb/add";//动态点赞

    public static final String URL_MOMENT_THUMB_CANCEL = "orange/moment/thumb/cancel";//动态取消赞

    public static final String URL_GET_SOCIAL_UNREAD = "orange/square/unread";//查询社区通知红泡数量
    public static final String URL_GET_LIMITED_ACTIVITY_TASK_EXPO = "orange/limited/activity/task/expo";//任务积分图标曝光接口

    public static final String URL_SOCIAL_ACCEPT_FRIEND_APPLY = "orange/community/interest/accept";//同意想认识
    public static final String URL_CIRCLE_MOMENT_LIST = "orange/circle/moment/list";//查询圈子动态列表

    public static final String URL_CIRCLE_JOIN = "orange/circle/join";//加入圈子

    public static final String URL_CIRCLE_QUIT = "orange/circle/quit";//退出圈子

    public static final String URL_MOMENT_COMMENT_THUMB_ADD = "orange/moment/replyThumb/add";//评论点赞

    public static final String URL_MOMENT_COMMENT_THUMB_CANCEL = "orange/moment/replyThumb/cancel";//评论取消赞

    public static final String URL_MOMENT_COMMENT_DELETE = "orange/moment/reply/delete";//删除评论

    public static final String URL_DATA_POINT = "orange/data/addPoint";//埋点

    public static final String URL_BATCH_UPDATE_SETTING = "orange/user/batchUpdateSetting";//批量更新用户设置接口

    public static final String URL_MOMENT_READ = "orange/moment/read";//阅读动态

    public static final String URL_PROFILE_STATUS = "orange/user/getProfileStatus";//查询用户信息完善情况

    public static final String URL_PROFILE_REPLY = "orange/profile/reply";//个人页评论接口

    //region 话题游戏相关接口
    public static final String URL_TOPIC_GAME_LABEL_LIST = "orange/topicGame/getLabelList";//话题游戏标签查询
    public static final String URL_TOPIC_GAME_QUESTION_LIST = "orange/topicGame/getQuestionList";//根据标签查询话题游戏问题
    public static final String URL_TOPIC_GAME_SEND = "orange/topicGame/send";//发送话题游戏
    public static final String URL_TOPIC_GAME_QUESTION_DETAIL = "orange/topicGame/getQuestionDetail";//话题游戏习题详情
    public static final String URL_TOPIC_GAME_REPLY = "orange/topicGame/reply";//回复话题游戏
    //endregion

    public static final String URL_REPLY_INFO = "orange/moment/replyInfo";//查询评论详情

    public static final String URL_SHAKE_SEARCH = "orange/shake/search";//心情共鸣-搜索

    public static final String URL_MATCH_GAMES_INFOS = "orange/matchGames/infos";//匹配信息

    public static final String URL_SHAKE_END = "orange/shake/end";//心情共鸣-停止搜索

    public static final String URL_SHAKE_CHECK = "orange/shake/check";//心情共鸣-倒计时结束后校验

    public static final String URL_SHAKE_PREPARE = "orange/shake/prepare";//心情共鸣-准备倒计时接口

    public static final String URL_MATCH_SKIP_HISTORY = "orange/match/skipHistory";//查询回看无感列表

    public static final String URL_CHAT_LIKE_LIST = "orange/match/myLikeList";//我喜欢的列表

    public static final String URL_LIKE_ME_PROFILE = "orange/match/likeMeProfile";//上下查看喜欢我的用户个人页信息

    public static final String URL_VIEW_LIKE_ME = "orange/chat/viewLikeMe";//查看喜欢我的用户

    public static final String URL_MOMENT_LATEST = "orange/moment/latest";//查询最新动态和个人信息


    public static final String URL_H5_MATCH_PREFERENCE = "match-preference.html";//匹配要素设置h5地址


    public static final String URL_H5_PERSONALITY_TEST = "testing.html";//性格测试h5地址

    public static final String URL_H5_COMPLAINT_GUIDE = "mine.html#/tort-complaint-guide";//侵权投诉

    public static final String URL_ME_CERT_CAR_SUBMIT = "orange/cert/car/submit";//车产认证提交

    public static final String URL_ME_CERT_CAR_DETAIL = "orange/cert/car/detail";//车产认证详情

    public static final String URL_ME_CERT_HOUSE_SUBMIT = "orange/cert/house/submit";//房产认证提交
    public static final String URL_ME_CERT_INCOME_SUBMIT = "orange/cert/income/submit";//收入认证提交
    public static final String URL_CERT_RANGE_CERT_COMPANY_SENDEMAIL = "orange/cert/company/sendEmail";//公司认证-邮箱认证

    public static final String URL_ME_CERT_HOUSE_DETAIL = "orange/cert/house/detail";//房产认证详情
    public static final String URL_ME_CERT_REVENUE_DETAIL = "orange/cert/income/detail";//收入认证详情
    public static final String URL_MATCHING_RECOMMEND_USERS = "orange/recommend/users/v1";//首页推荐用户
    public static final String URL_MATCHING_RECOMMEND_ACTIVITY_GUIDE = "orange/recommend/activityGuide";//首页推荐活动
    public static final String URL_MATCHING_LIMITED_ACTIVITY_TASK_RCDCARD_EXCHANGE = "orange/limited/activity/task/rcdCard/exchange";//圣诞活动卡片积分兑换
    public static final String URL_MATCHING_ACTIVITY_USERS = "orange/activity/users";//活动嘉宾列表
    public static final String URL_MATCHING_RECOMMEND_IS_UPDATE = "orange/recommend/isUpdate";//首页推荐用户是否已经更新
    public static final String URL_MATCHING_LIKE_ME_PAGE = "orange/interactive/likeMePage";//喜欢我列表
    public static final String URL_MATCHING_LIKE_ME_LIST = "orange/interactive/likeMeList";//喜欢我列表分页
    public static final String URL_MATCHING_I_LIKE_PAGE = "orange/interactive/myLikedPage";//我喜欢的列表分页

    public static final String URL_MATCHING_VIEW_ME_PAGE = "orange/interactive/viewMePage";//看过我列表
    public static final String URL_MATCHING_VIEW_ME_LIST = "orange/interactive/viewMeList";//看过我列表分页
    public static final String URL_MATCHING_VIEW_LOOK_TAB = "orange/interactive/viewLookMeTab";//看过我列表分页
    public static final String URL_PARENT_UNBIND = "orange/parent/unbind";//解除绑定
    public static final String URL_PARENT_CANCEL_INVITE_BIND = "orange/parent/cancelInviteBind";//取消绑定邀请
    public static final String URL_PARENT_PARENT_SHARE_LIST = "orange/child/forwardUserList";//父母转发记录

    public static final String URL_PARENT_INVITE_BIND_ACCEPT = "orange/parent/inviteBindAccept";//接受绑定邀请
    public static final String URL_PARENT_INVITE_BIND_REFUSE = "orange/parent/inviteBindRefuse";//拒绝绑定邀请

    public static final String URL_PARENT_INVITE_INVITE_BIND_LIST = "orange/parent/inviteBindList";//查询待处理绑定列表
    public static final String URL_VIDEO_MEETING_AUTH = "orange/link/auth";//获取通话凭证
    public static final String URL_FOUNDATION_SEND_LIKE_CHECK = "orange/match/like/block";//发送喜欢前检查
    public static final String URL_VIEW_LIKE_ME_V2 = "orange/interactive/viewLikeMe";//查看喜欢我的用户
    public static final String URL_UPDATE_VIEW_READ = "orange/interactive/updateViewRead";//看过我红点消除
    public static final String URL_CHAT_DEL_MESSAGE = "orange/chat/delMessage";//删除单条消息
    public static final String URL_CHAT_CLEAR_MESSAGE = "orange/chat/clearMessage";//清空聊天记录
    public static final String URL_USER_SAFE = "orange/user/safe";//查询用户安全信息
    public static final String URL_PHONE_CURRENT_SEND_CODE = "orange/user/safe/phone/current/sendCode";//给原手机号发送验证码
    public static final String URL_PHONE_CURRENT_VERIFY_CODE = "orange/user/safe/phone/current/verifyCode";//验证当前手机号验证码
    public static final String URL_PHONE_CHANGE = "orange/user/safe/phone/change";//修改手机号
    public static final String URL_WECHAT_CHANGE = "orange/user/safe/wechat/change";//修改微信号
    public static final String URL_PHONE_CHANGE_SEND_CODE = "orange/user/safe/phone/sendCode";//给手机号发送验证码（修改手机号用）
    public static final String URL_WE_CHAT_LOGIN = "orange/auth/wechatLogin";//微信登录
    public static final String URL_AGREEMENT_DETAIL = "orange/agreement/detail";//查询协议详情
    public static final String URL_USER_QUESTION_INFO = "orange/user/questionInfo";//获取问题详情
    public static final String URL_USER_SAFE_WECHAT_UNBIND = "orange/user/safe/wechat/unbind";//微信解绑
    public static final String URL_CHAT_STICKER_LIST_V1 = "orange/sticker/list/v1";//获取表情包列表v1
    public static final String URL_CHAT_STICKER_RECOMMEND = "orange/sticker/recommend";//获取预设推荐表情
    public static final String URL_CHAT_GET_USER_CARD = "orange/chat/getUserCard";//查询用户资料卡片
    public static final String URL_CHAT_GET_YUEYUE_REC_MSG = "orange/chat/getYueyueRecMsg";//获取悦悦推荐消息
    public static final String URL_CHAT_GET_YUEYUE_TOP_CARD = "orange/chat/getYueyueTopCard";//获取悦悦顶部卡片
    public static final String URL_CHAT_REFRESH_RECOMMEND_USER_MSG = "orange/chat/refreshRecommendUserMsg";//刷新推荐用户消息
    public static final String URL_MEETUP_CHAT_PLAN_CARD = "orange/meetup/chat/planCard";//见面计划聊天头部卡片
    public static final String URL_ME_ADD_SINGLE_TAG = "orange/user/addSingleTag";//添加单个标签
    public static final String URL_ORANGE_QR_GETPROTO = "orange/qr/getProto";//获取用户信息二维码跳转协议
    public static final String URL_ORANGE_SECURITY_GET = "orange/security/get";//查询用户是否存在处置
    public static final String URL_ORANGE_MOOD_SUBMIT = "orange/mood/submit";//提交心情
    public static final String URL_MOOD_EDIT_DETAIL = "orange/mood/edit/detail";//主态心情详情
    public static final String URL_MOOD_OTHER_DETAIL = "orange/mood/otherView/detail";//客态心情详情
    public static final String URL_MOOD_EXPIRE = "orange/mood/expire";//结束心情
    public static final String URL_MOOD_LIKE = "orange/mood/thumb/add";//心情点赞

    // const val URL_COMMON_GPS_LOCATION = "orange/common/gpsLocation" // 查询gps对应地址
    public static final String URL_COMMON_GPS_LOCATION = "orange/common/gpsLocation";

    // 头像编辑/替换流程灰度
    public static final String URL_USER_AVATAR_ADVICE_GRAY = "orange/user/avatar/advice/gray";

    // 微信服务号相关接口
    public static final String URL_WECHAT_SERVICE_SETTING_GET = "orange/wechat/service/setting/get"; // 获取微信服务号设置
    public static final String URL_WECHAT_SERVICE_NOTICE_DIALOG = "orange/wechat/service/notice/dialog"; // 获取微信通知弹窗数据
    public static final String URL_WECHAT_SERVICE_NOTICE_CLOSE = "orange/wechat/service/notice/close"; // 关闭微信通知弹窗
    public static final String URL_WECHAT_SERVICE_GET_SVC_SECURITY_CODE = "orange/wechat/service/getSvcSecurityCode"; // 获取微信服务安全码

    // 学历认证相关接口
    public static final String URL_CERT_EDU_ADVICE_AGREE = "orange/cert/edu/advice/agree"; // 同意学历认证建议

    // 见面计划相关接口
    public static final String URL_MEETUP_CHAT_UNLOCK_CLICK = "orange/meetup/chat/unLockClick"; // 见面计划聊天解锁点击
    public static final String URL_MEETUP_CHAT_INVITE_REFUSE = "orange/meetup/chat/inviteRefuse"; // 拒绝见面计划聊天邀请
    public static final String URL_MEETUP_CHAT_INVITE_ACCEPT = "orange/meetup/chat/inviteAccept"; // 接受见面计划聊天邀请

    // 微信交换相关接口
    public static final String URL_WECHAT_EXCHANGE_SAVE_WECHAT = "orange/wechat/exchange/saveWechat"; // 保存微信号
    public static final String URL_WECHAT_EXCHANGE_SEND_WECHAT = "orange/wechat/exchange/sendWechat"; // 发送微信号
    public static final String URL_WECHAT_EXCHANGE_CHECK_USER = "orange/wechat/exchange/checkUser"; // 检查微信交换用户

    // 聊天状态上报相关接口
    public static final String URL_CHAT_REPORT_SEND_STATUS = "orange/chat/reportSendStatus"; // 上报发送状态

    // 位置共享相关接口
    public static final String URL_LOCATION_SHARE_SEND = "orange/location/share/send"; // 发送位置共享

    // 见面保护相关接口
    public static final String URL_PROTECT_MEET_INVITE_CARD = "orange/protect/meet/invite/card"; // 获取见面保护邀请卡片
    public static final String URL_PROTECT_MEET_INVITE_REFUSE = "orange/protect/meet/invite/refuse"; // 拒绝见面保护邀请
    public static final String URL_PROTECT_MEET_INVITE_ACCEPT = "orange/protect/meet/invite/accept"; // 接受见面保护邀请
    public static final String URL_PROTECT_MEET_CANCEL_REASON_LIST = "orange/protect/meet/cancelReason/list"; // 获取见面取消原因列表
    public static final String URL_PROTECT_MEET_CANCEL = "orange/protect/meet/cancel"; // 取消见面
    public static final String URL_PROTECT_MEET_SETTING_GOTO = "orange/protect/meet/setting/goto"; // 去设置见面
    public static final String URL_PROTECT_MEET_SETTING_ACCEPT = "orange/protect/meet/setting/accept"; // 接受见面设置
    public static final String URL_PROTECT_MEET_SETTING_CHOOSE_AI = "orange/protect/meet/setting/chooseAi"; // 选择AI推荐
    public static final String URL_PROTECT_MEET_SETTING_INVITE = "orange/protect/meet/setting/invite"; // 邀请设置见面

    // 见面状态和评价相关接口
    public static final String URL_MINIAPP_PROTECT_MEET_STATUS_SUBMIT = "orange/miniapp/protect/meet/status/submit"; // 提交见面状态
    public static final String URL_MINIAPP_PROTECT_MEET_REVIEW_ADD = "orange/miniapp/protect/meet/review/add"; // 添加见面评价

    // 星准相关接口
    public static final String URL_STAR_SIGNUP_POP = "orange/star/signupPop"; // 获取星准引导页数据
    public static final String URL_STAR_SIGNUP_ACCEPT = "orange/star/signup/accept"; // 接受星准邀请
    public static final String URL_STAR_SIGNUP_REFUSE = "orange/star/signup/refuse"; // 拒绝星准邀请


    public static List<String> FILE_H5_LIST = Arrays.asList(URL_H5_MATCH_PREFERENCE, URL_H5_PERSONALITY_TEST, URL_H5_COMPLAINT_GUIDE);

    public static boolean unknowWeb(String url) {
        if (TextUtils.isEmpty(url)) {
            return true;
        }
        return HostConfig.getCONFIG() != null &&
                !URLUtils.getMainHost(url).endsWith("bosszhipin.com") &&
                !URLUtils.getMainHost(url).endsWith("kanzhun.com") &&
                !URLUtils.getMainHost(url).endsWith("dianzhangzhipin.com") &&
                !URLUtils.getMainHost(url).endsWith("sobot.com") &&
                !URLUtils.getMainHost(url).endsWith("zhipin.com") &&
                !URLUtils.getMainHost(url).equals("zpurl.cn") &&
                !URLUtils.getMainHost(url).equals("weizhipin.com") &&
                !url.startsWith(HostConfig.getCONFIG().getApiAddr()) &&
                !url.startsWith(HostConfig.getCONFIG().getLoadAddr()) &&
                !url.startsWith(HostConfig.getCONFIG().getWebAddr()) &&
                !url.startsWith(HostConfig.getCONFIG().getMqttAddr()) &&
                !url.startsWith(HostConfig.getCONFIG().getVideoChatAddr()) &&
                !url.startsWith(HostConfig.getCONFIG().getLogUploadAddr());
    }
}
