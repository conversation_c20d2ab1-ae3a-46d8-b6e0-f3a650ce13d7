# 编码规范总结

## 概述

本文档总结了项目中观察到的编码规范和最佳实践，旨在保持代码的一致性和可维护性。

## 命名规范

### 包名规范
```
com.kanzhun.marry.{module}.{layer}.{specific}

示例:
- com.kanzhun.marry.chat.activity
- com.kanzhun.marry.me.viewmodel  
- com.kanzhun.foundation.base.activity
```

### 类命名规范

#### Activity命名
```java
// 格式: {功能描述}Activity
public class MeSettingActivity extends FoundationVMActivity<...>
public class ChatDetailActivity extends FoundationVMActivity<...>
public class VideoPlayActivity extends FoundationVMActivity<...>
```

#### ViewModel命名
```java
// 格式: {功能描述}ViewModel
public class MeSettingViewModel extends FoundationViewModel
public class ChatDetailViewModel extends FoundationViewModel
```

#### Fragment命名
```java
// 格式: {功能描述}Fragment
public class MeInfoPreviewFragment extends FoundationVMFragment<...>
```

#### Adapter命名
```java
// 格式: {功能描述}Adapter
public class ChatMessageAdapter extends BaseBinderAdapter
```

### 资源文件命名

#### Layout文件
```xml
<!-- Activity布局: {module}_activity_{功能}.xml -->
me_activity_setting.xml
chat_activity_detail.xml

<!-- Fragment布局: {module}_fragment_{功能}.xml -->
me_fragment_info_preview.xml

<!-- Item布局: {module}_item_{功能}.xml -->
chat_item_message.xml
```

#### Drawable资源
```xml
<!-- 图标: ic_{功能}_{状态}.xml -->
ic_chat_send_normal.xml
ic_me_setting_selected.xml

<!-- 背景: bg_{功能}_{状态}.xml -->
bg_button_pressed.xml
bg_card_normal.xml
```

#### String资源
```xml
<!-- 格式: {module}_{功能}_{描述} -->
<string name="me_setting_title">设置</string>
<string name="chat_send_message_hint">请输入消息</string>
```

## 代码结构规范

### 包结构规范
```
com.kanzhun.marry.{module}/
├── activity/          # Activity类
├── fragment/          # Fragment类  
├── viewmodel/         # ViewModel类
├── adapter/           # 适配器类
├── model/             # 数据模型
├── api/               # API接口定义
├── repository/        # 数据仓库
├── util/              # 工具类
├── dialog/            # 对话框
├── views/             # 自定义View
└── callback/          # 回调接口
```

### Activity结构规范

```java
@RouterUri(path = MePageRouter.ME_SETTING)
public class MeSettingActivity extends FoundationVMActivity<MeActivitySettingBinding, MeSettingViewModel> 
    implements MeSettingCallback {

    // 1. 常量定义
    private static final String TAG = "MeSettingActivity";
    private static final int REQUEST_CODE = 1001;

    // 2. 成员变量
    private BaseBinderAdapter adapter;
    private View customView;

    // 3. 生命周期方法
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
        initData();
    }

    // 4. 初始化方法
    private void initView() {
        // UI初始化
    }

    private void initData() {
        // 数据初始化
    }

    // 5. 事件处理方法
    @Override
    public void onSettingClick() {
        // 处理设置点击
    }

    // 6. 工具方法
    private void showDialog() {
        // 显示对话框
    }
}
```

### ViewModel结构规范

```java
public class MeSettingViewModel extends FoundationViewModel {

    // 1. LiveData定义
    private MutableLiveData<List<SettingItem>> settingItems = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();

    // 2. ObservableField定义
    private ObservableField<String> title = new ObservableField<>();
    private ObservableBoolean isEnabled = new ObservableBoolean();

    // 3. 构造方法
    public MeSettingViewModel(Application application) {
        super(application);
        initData();
    }

    // 4. 公共方法
    public void loadSettings() {
        // 加载设置数据
    }

    public void updateSetting(SettingItem item) {
        // 更新设置
    }

    // 5. 私有方法
    private void initData() {
        // 初始化数据
    }

    // 6. Getter方法
    public MutableLiveData<List<SettingItem>> getSettingItems() {
        return settingItems;
    }
}
```

## 注解使用规范

### 路由注解
```java
// 页面路由注册
@RouterUri(path = MePageRouter.ME_SETTING)
public class MeSettingActivity extends FoundationVMActivity<...>

// 路由路径定义
public class MePageRouter {
    public static final String ME_SETTING = "/me/setting";
    public static final String ME_PROFILE = "/me/profile";
}
```

### 数据库注解
```java
@Entity(tableName = "user_info")
public class UserInfo {
    @PrimaryKey
    @NonNull
    public String userId;
    
    @ColumnInfo(name = "nick_name")
    public String nickName;
}

@Dao
public interface UserInfoDao {
    @Query("SELECT * FROM user_info WHERE userId = :userId")
    UserInfo getUserInfo(String userId);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertUser(UserInfo user);
}
```

### 网络请求注解
```java
public interface ChatApi {
    @GET("chat/messages")
    Observable<BaseResponse<List<ChatMessage>>> getMessages(
        @Query("chatId") String chatId,
        @Query("page") int page
    );
    
    @POST("chat/send")
    Observable<BaseResponse<Void>> sendMessage(
        @Body SendMessageRequest request
    );
}
```

## 编码最佳实践

### 1. 空安全处理
```java
// 使用@Nullable和@NonNull注解
public void updateUser(@Nullable UserInfo user) {
    if (user != null) {
        // 处理用户信息
    }
}

// Kotlin空安全
fun updateUser(user: UserInfo?) {
    user?.let {
        // 处理用户信息
    }
}
```

### 2. 资源管理
```java
// 正确关闭资源
try (FileInputStream fis = new FileInputStream(file)) {
    // 读取文件
} catch (IOException e) {
    // 处理异常
}
```

### 3. 异步处理
```java
// 使用RxJava处理异步操作
Observable.fromCallable(() -> {
    // 后台任务
    return loadData();
})
.subscribeOn(Schedulers.io())
.observeOn(AndroidSchedulers.mainThread())
.subscribe(
    data -> {
        // 处理成功结果
    },
    error -> {
        // 处理错误
    }
);
```

### 4. 内存优化
```java
// 使用弱引用避免内存泄漏
private WeakReference<Activity> activityRef;

// 及时释放资源
@Override
protected void onDestroy() {
    super.onDestroy();
    if (disposable != null && !disposable.isDisposed()) {
        disposable.dispose();
    }
}
```

## 代码注释规范

### 类注释
```java
/**
 * 个人设置页面
 * 
 * 功能包括:
 * 1. 账户安全设置
 * 2. 隐私设置  
 * 3. 通知设置
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
public class MeSettingActivity extends FoundationVMActivity<...>
```

### 方法注释
```java
/**
 * 更新用户头像
 * 
 * @param avatarUrl 头像URL地址
 * @param callback 更新结果回调
 */
public void updateAvatar(String avatarUrl, UpdateCallback callback) {
    // 实现逻辑
}
```

### 复杂逻辑注释
```java
// 计算用户匹配度算法
// 1. 基础信息匹配 (30%)
// 2. 兴趣爱好匹配 (40%) 
// 3. 地理位置匹配 (30%)
private float calculateMatchScore(UserInfo user1, UserInfo user2) {
    // 实现算法
}
```

## Git提交规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例
```
feat(chat): 添加语音消息功能

- 支持录制和播放语音消息
- 添加语音消息UI组件
- 集成音频录制和播放功能

Closes #123
```

## 性能优化规范

### 1. 布局优化
- 减少布局层级嵌套
- 使用ConstraintLayout替代复杂的LinearLayout嵌套
- 合理使用ViewStub延迟加载

### 2. 内存优化
- 及时释放不需要的对象引用
- 使用对象池复用对象
- 避免在循环中创建对象

### 3. 网络优化
- 合并网络请求
- 使用缓存减少重复请求
- 压缩请求和响应数据

### 4. 图片优化
- 根据ImageView尺寸加载合适大小的图片
- 使用WebP格式减少图片大小
- 实现图片懒加载

## 测试规范

### 单元测试
```java
@Test
public void testCalculateMatchScore() {
    UserInfo user1 = createTestUser1();
    UserInfo user2 = createTestUser2();
    
    float score = matchService.calculateMatchScore(user1, user2);
    
    assertEquals(0.85f, score, 0.01f);
}
```

### UI测试
```java
@Test
public void testLoginFlow() {
    onView(withId(R.id.et_username))
        .perform(typeText("testuser"));
    onView(withId(R.id.et_password))
        .perform(typeText("password"));
    onView(withId(R.id.btn_login))
        .perform(click());
    
    onView(withText("登录成功"))
        .check(matches(isDisplayed()));
}
```

---

*本规范基于项目当前代码分析总结，建议团队成员严格遵守以保证代码质量和一致性。*