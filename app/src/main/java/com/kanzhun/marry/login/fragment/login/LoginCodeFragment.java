package com.kanzhun.marry.login.fragment.login;


import android.view.View;

import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.kanzhun.common.dialog.DialogExtKt;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.views.edittext.OnInputListener;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.router.LoginPageRouter;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.BR;
import com.kanzhun.marry.BuildConfig;
import com.kanzhun.marry.R;
import com.kanzhun.marry.databinding.FragmentLoginCodeBinding;
import com.kanzhun.marry.helper.LoginHelper;
import com.kanzhun.marry.login.callback.LoginCodeCallback;
import com.kanzhun.marry.login.point.LoginPointAction;
import com.kanzhun.marry.login.viewmodel.LoginCodeViewModel;
import com.kanzhun.marry.login.viewmodel.LoginViewModel;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/10
 * 输入验证码页面
 */
public class LoginCodeFragment extends FoundationVMShareFragment<FragmentLoginCodeBinding, LoginCodeViewModel, LoginViewModel>
        implements LoginCodeCallback {
    @Override
    public int getContentLayoutId() {
        return R.layout.fragment_login_code;
    }

    @Override
    protected void initFragment() {
        initView();
        getActivityViewModel().getInputSmsCodeSuccessLivaData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean != null) {
                    if (aBoolean) {
                        Navigation.findNavController(getDataBinding().editText).navigate(R.id.action_inputCodeFragment_to_selectModelFragment);
                        getActivityViewModel().getInputSmsCodeSuccessLivaData().setValue(null);
                    } else {
                        getDataBinding().editText.setText("");
                        showInput();
                    }
                }
            }
        });
        getActivityViewModel().getSelectModelCodeSuccessLivaData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean != null) {
                    if (aBoolean) {
                        LoginPageRouter.jumpToSelectModel(getContext(), true);
                        AppUtil.finishActivity(getContext());
//                        Navigation.findNavController(getDataBinding().editText).navigate(R.id.action_inputCodeFragment_to_selectModelFragment);
//                        getActivityViewModel().getSelectModelCodeSuccessLivaData().setValue(null);
                    } else {
                        getDataBinding().editText.setText("");
                        showInput();
                    }
                }

            }
        });
        getActivityViewModel().getInputSuccessLivaData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    //在验证码页面收到发送验证码成功时 不需要跳转到验证码页面
                    getActivityViewModel().getInputSuccessLivaData().setValue(false);
                }

            }
        });

        getActivityViewModel().getWechatHasBind().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    DialogExtKt.showTwoButtonDialog(requireActivity(), "绑定提示",
                            "此手机号已有绑定微信号，是否直接去登录",
                            false, false, "去登录", new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    requireActivity().finish();
                                    return null;
                                }
                            }, "取消", new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    NavController navController = Navigation.findNavController(requireActivity(), R.id.fragment);
                                    navController.popBackStack();
                                    return null;
                                }
                            }, false);
                }
            }
        });
        if (BuildConfig.DEBUG) {
            new LoginHelper().startLogin(getActivityViewModel().phoneNumberObservable.get(), new Function1<String, Unit>() {
                @Override
                public Unit invoke(String s) {
                    getActivityViewModel().login(s);
                    return null;
                }
            });
        }
        PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_VERIFICTIONCODE_EXPO, null);
    }

    private void initView() {
        showInput();
        getDataBinding().tvPhoneTitle.setSubTitle(getResources().getString(R.string.login_code_to_phone_number
                , getActivityViewModel().getEncryptPhoneNumber()));
        getDataBinding().editText.setOnInputListener(new OnInputListener() {
            @Override
            public void onInputFinished(String content) {
                QMUIKeyboardHelper.hideKeyboard(getDataBinding().editText);
                getDataBinding().editText.postDelayed(() -> getActivityViewModel().login(content), 200);
            }

            @Override
            public void onInputChanged(String text) {
                super.onInputChanged(text);
            }
        });
        getDataBinding().editText.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_VERIFICTIONCODE_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType("验证码输入框");
                        return null;
                    }
                });
            }
        });
        getDataBinding().tvReSend.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                getDataBinding().editText.setText("");
                getActivityViewModel().sendCode();
            }
        });
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public void onBack() {
        if (getActivity() != null) {
            getActivity().onBackPressed();
        }
    }

    @Override
    public void onSkip() {

    }

    @Override
    public void showInput() {
        QMUIKeyboardHelper.showKeyboard(getDataBinding().editText, true);
    }

}
